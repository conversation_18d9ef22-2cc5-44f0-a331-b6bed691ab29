"""
Popup Configuration Manager Backend Script
Aggregates all popup configuration and management functionality.
"""
import os
import json
import logging
import base64
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime
from pathlib import Path

class PopupConfigurationManager:
    """Manager for popup configurations"""

    def __init__(self, config_file: str = 'popupdata.json'):
        """
        Initialize the popup configuration manager.

        Args:
            config_file: Path to the popup configuration JSON file
        """
        self.config_file = config_file
        self.logger = self._setup_logger()
        self.default_config = self._get_default_config()

    def _setup_logger(self):
        """Set up logger for the configuration manager"""
        logger = logging.getLogger("openengage.popup.config_manager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _get_default_config(self) -> Dict[str, Any]:
        """
        Get default popup configuration.

        Returns:
            Dict containing default popup configuration
        """
        return {
            "popup_title": "Welcome to OpenEngage",
            "popup_text": "",
            "popup_background_image": "",
            "popup_button_text": "Visit OpenEngage Website",
            "popup_button_redirect_url": "https://openengage.ai",
            "popup_image_opacity": 0.82,
            "popup_delay_seconds": 2,
            "popup_type": "banner",  # banner or form
            "popup_form_placeholder": "Enter your email or phone number",
            "popup_primary_color": "#2674ED",
            "popup_button_color": "#F9C823"
        }

    def load_configuration(self) -> Dict[str, Any]:
        """
        Load popup configuration from file.

        Returns:
            Dict containing popup configuration
        """
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                self.logger.info(f"Loaded popup configuration from {self.config_file}")
                return config
            except json.JSONDecodeError as e:
                self.logger.warning(f"Error parsing {self.config_file}: {str(e)}. Using default configuration.")
                return self.default_config.copy()
        else:
            self.logger.info(f"Configuration file {self.config_file} not found. Using default configuration.")
            return self.default_config.copy()

    def save_configuration(self, config: Dict[str, Any]) -> bool:
        """
        Save popup configuration to file.

        Args:
            config: Configuration dictionary to save

        Returns:
            True if successful, False otherwise
        """
        try:
            # Validate configuration
            validated_config = self._validate_configuration(config)
            
            with open(self.config_file, 'w') as f:
                json.dump(validated_config, f, indent=4)
            
            self.logger.info(f"Popup configuration saved to {self.config_file}")
            return True
        except Exception as e:
            self.logger.error(f"Error saving popup configuration: {str(e)}")
            return False

    def _validate_configuration(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and sanitize popup configuration.

        Args:
            config: Configuration to validate

        Returns:
            Validated configuration dictionary
        """
        validated = self.default_config.copy()
        
        # Update with provided values, ensuring types are correct
        for key, default_value in self.default_config.items():
            if key in config:
                if isinstance(default_value, (int, float)):
                    try:
                        validated[key] = type(default_value)(config[key])
                    except (ValueError, TypeError):
                        validated[key] = default_value
                elif isinstance(default_value, str):
                    validated[key] = str(config[key])
                else:
                    validated[key] = config[key]
        
        # Additional validation
        if validated["popup_delay_seconds"] < 0:
            validated["popup_delay_seconds"] = 0
        elif validated["popup_delay_seconds"] > 30:
            validated["popup_delay_seconds"] = 30
            
        if not (0.0 <= validated["popup_image_opacity"] <= 1.0):
            validated["popup_image_opacity"] = 0.82
            
        if validated["popup_type"] not in ["banner", "form"]:
            validated["popup_type"] = "banner"
        
        return validated

    def update_configuration(self, updates: Dict[str, Any]) -> bool:
        """
        Update specific fields in the popup configuration.

        Args:
            updates: Dictionary of fields to update

        Returns:
            True if successful, False otherwise
        """
        current_config = self.load_configuration()
        current_config.update(updates)
        return self.save_configuration(current_config)

    def get_brand_colors(self) -> List[str]:
        """
        Get brand colors from brand guidelines.

        Returns:
            List of brand colors
        """
        try:
            brand_guidelines_path = 'data/brand_guidelines.json'
            if os.path.exists(brand_guidelines_path):
                with open(brand_guidelines_path, 'r') as f:
                    brand_data = json.load(f)
                    colors = brand_data.get('colors', {})
                    return [
                        colors.get('primary', '#2674ED'),
                        colors.get('secondary', '#F9C823'),
                        colors.get('accent', '#FF6B6B'),
                        '#2674ED',  # Default blue
                        '#F9C823',  # Default yellow
                        '#FF6B6B',  # Default red
                        '#4ECDC4',  # Teal
                        '#45B7D1',  # Light blue
                        '#96CEB4',  # Light green
                        '#FFEAA7'   # Light yellow
                    ]
        except Exception as e:
            self.logger.warning(f"Error loading brand colors: {str(e)}")
        
        # Return default colors if brand guidelines not available
        return ['#2674ED', '#F9C823', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']

    def save_background_image(self, image_data: bytes, filename: str) -> str:
        """
        Save popup background image.

        Args:
            image_data: Image data as bytes
            filename: Original filename

        Returns:
            Saved filename or empty string if failed
        """
        try:
            # Create images directory if it doesn't exist
            os.makedirs('images', exist_ok=True)
            
            # Generate unique filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_extension = os.path.splitext(filename)[1]
            saved_filename = f"popup_bg_{timestamp}{file_extension}"
            saved_path = os.path.join('images', saved_filename)
            
            # Save the image
            with open(saved_path, 'wb') as f:
                f.write(image_data)
            
            self.logger.info(f"Background image saved as {saved_filename}")
            return saved_filename
        except Exception as e:
            self.logger.error(f"Error saving background image: {str(e)}")
            return ""

    def get_popup_preview_html(self, config: Dict[str, Any]) -> str:
        """
        Generate HTML preview of the popup.

        Args:
            config: Popup configuration

        Returns:
            HTML string for popup preview
        """
        # Extract configuration values
        title = config.get("popup_title", "Welcome")
        text = config.get("popup_text", "")
        button_text = config.get("popup_button_text", "Learn More")
        primary_color = config.get("popup_primary_color", "#2674ED")
        button_color = config.get("popup_button_color", "#F9C823")
        popup_type = config.get("popup_type", "banner")
        form_placeholder = config.get("popup_form_placeholder", "Enter your email or phone number")
        background_image = config.get("popup_background_image", "")
        opacity = config.get("popup_image_opacity", 0.82)

        # Generate background CSS
        background_css = ""
        if background_image:
            background_css = f"""
                background-image: linear-gradient(rgba(255,255,255,{1-opacity}), rgba(255,255,255,{1-opacity})), url('{background_image}');
                background-size: cover;
                background-position: center;
            """

        if popup_type == "form":
            return f"""
            <div style="
                border: 1px solid #ddd;
                border-radius: 10px;
                padding: 20px;
                margin: 20px 0;
                {background_css}
                text-align: center;
                width: 100%;
                max-width: 500px;
                margin: 0 auto;
            ">
                <h3 style="color: {primary_color};">{title}</h3>
                <p>{text}</p>
                <div style="margin: 15px 0;">
                    <input type="text" placeholder="{form_placeholder}" style="
                        width: 100%;
                        padding: 10px;
                        margin-bottom: 15px;
                        border: 1px solid #ddd;
                        border-radius: 5px;
                        font-size: 16px;
                    ">
                    <button style="
                        background-color: {button_color};
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 5px;
                        cursor: pointer;
                        font-size: 16px;
                        width: 100%;
                    ">{button_text}</button>
                </div>
            </div>
            """
        else:  # banner type
            return f"""
            <div style="
                border: 1px solid #ddd;
                border-radius: 10px;
                padding: 20px;
                margin: 20px 0;
                {background_css}
                text-align: center;
                width: 100%;
                max-width: 500px;
                margin: 0 auto;
                cursor: pointer;
            ">
                <h3 style="color: {primary_color};">{title}</h3>
                <p>{text}</p>
                <button style="
                    background-color: {button_color};
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 16px;
                    margin-top: 10px;
                ">{button_text}</button>
            </div>
            """

    def load_popup_user_data(self, csv_file: str = 'data/popup_text.csv') -> Optional[pd.DataFrame]:
        """
        Load popup user data from CSV file.

        Args:
            csv_file: Path to the CSV file

        Returns:
            DataFrame with popup user data or None if not found
        """
        try:
            if os.path.exists(csv_file):
                df = pd.read_csv(csv_file)
                self.logger.info(f"Loaded popup user data from {csv_file}")
                return df
            else:
                self.logger.warning(f"Popup user data file {csv_file} not found")
                return None
        except Exception as e:
            self.logger.error(f"Error loading popup user data: {str(e)}")
            return None

    def get_user_popup_data(self, user_email: str, csv_file: str = 'data/popup_text.csv') -> Optional[Dict[str, Any]]:
        """
        Get popup data for a specific user.

        Args:
            user_email: Email of the user
            csv_file: Path to the CSV file

        Returns:
            Dictionary with user popup data or None if not found
        """
        df = self.load_popup_user_data(csv_file)
        if df is not None:
            user_data = df[df['user_email'] == user_email]
            if not user_data.empty:
                return user_data.iloc[0].to_dict()
        return None


# Main functions for external use

def create_popup_configuration(config_data: Dict[str, Any], config_file: str = 'popupdata.json') -> Dict[str, Any]:
    """
    Create and save a new popup configuration.

    Args:
        config_data: Configuration data dictionary
        config_file: Path to configuration file

    Returns:
        Dict with operation results
    """
    manager = PopupConfigurationManager(config_file)
    
    success = manager.save_configuration(config_data)
    
    return {
        "success": success,
        "config_file": config_file,
        "message": "Popup configuration saved successfully" if success else "Failed to save popup configuration"
    }


def load_popup_configuration(config_file: str = 'popupdata.json') -> Dict[str, Any]:
    """
    Load popup configuration from file.

    Args:
        config_file: Path to configuration file

    Returns:
        Dict containing popup configuration
    """
    manager = PopupConfigurationManager(config_file)
    config = manager.load_configuration()
    
    return {
        "success": True,
        "configuration": config,
        "config_file": config_file
    }


def update_popup_configuration(updates: Dict[str, Any], config_file: str = 'popupdata.json') -> Dict[str, Any]:
    """
    Update specific fields in popup configuration.

    Args:
        updates: Dictionary of fields to update
        config_file: Path to configuration file

    Returns:
        Dict with operation results
    """
    manager = PopupConfigurationManager(config_file)
    
    success = manager.update_configuration(updates)
    
    return {
        "success": success,
        "updated_fields": list(updates.keys()),
        "message": "Popup configuration updated successfully" if success else "Failed to update popup configuration"
    }


def get_popup_preview(config_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate popup preview HTML.

    Args:
        config_data: Popup configuration data

    Returns:
        Dict containing preview HTML and configuration
    """
    manager = PopupConfigurationManager()
    
    preview_html = manager.get_popup_preview_html(config_data)
    
    return {
        "success": True,
        "preview_html": preview_html,
        "configuration": config_data
    }


def save_popup_background_image(image_data: bytes, filename: str) -> Dict[str, Any]:
    """
    Save popup background image.

    Args:
        image_data: Image data as bytes
        filename: Original filename

    Returns:
        Dict with operation results
    """
    manager = PopupConfigurationManager()
    
    saved_filename = manager.save_background_image(image_data, filename)
    
    return {
        "success": bool(saved_filename),
        "saved_filename": saved_filename,
        "message": f"Image saved as {saved_filename}" if saved_filename else "Failed to save image"
    }


def get_brand_colors() -> Dict[str, Any]:
    """
    Get available brand colors for popup configuration.

    Returns:
        Dict containing brand colors
    """
    manager = PopupConfigurationManager()
    colors = manager.get_brand_colors()
    
    return {
        "success": True,
        "colors": colors,
        "count": len(colors)
    }
