"""
Email Sender Backend Script
Aggregates all email sending functionality including ESP integrations and delivery management.
"""
import os
import json
import logging
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dotenv import load_dotenv
import uuid

# Load environment variables
load_dotenv()

class EmailSender:
    """Base class for email sending"""

    def __init__(self, api_key: str):
        """Initialize the email sender with API key"""
        self.api_key = api_key
        self.logger = self._setup_logger()
        self.from_email = ""
        self.sender_name = ""
        self.reply_to_email = ""

    def _setup_logger(self):
        """Set up logger for the sender"""
        logger = logging.getLogger("openengage.email.sender")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def set_sender_details(self, sender_name: str, sender_email: str, reply_to_email: str):
        """Set sender details"""
        self.sender_name = sender_name
        self.from_email = sender_email
        self.reply_to_email = reply_to_email
        self.logger.info(f"Sender details set: {sender_name} <{sender_email}>")

    def send_emails(self, email_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Send emails using the configured provider.

        Args:
            email_data: DataFrame containing email data

        Returns:
            Dict with results of the send operation
        """
        raise NotImplementedError("Subclasses must implement send_emails")

    def schedule_emails(self, email_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Schedule emails for future delivery.

        Args:
            email_data: DataFrame containing email data with Send_Time column

        Returns:
            Dict with results of the schedule operation
        """
        raise NotImplementedError("Subclasses must implement schedule_emails")


class SparkPostSender(EmailSender):
    """SparkPost implementation of email sender"""

    def __init__(self, api_key: str):
        """Initialize the SparkPost sender with API key"""
        super().__init__(api_key)
        
        # Get sender details from environment variables
        sender_name = os.getenv("EMAIL_SENDER_NAME", "OpenEngage Team")
        sender_email = os.getenv("EMAIL_SENDER_EMAIL", "<EMAIL>")
        reply_to_email = os.getenv("EMAIL_REPLY_TO", "<EMAIL>")
        
        # Set default sender details
        self.set_sender_details(sender_name, sender_email, reply_to_email)
        
        # Validate API key
        self.validate_api_key()

    def validate_api_key(self):
        """Validate the SparkPost API key"""
        try:
            # Import SparkPost here to avoid dependency issues
            from sparkpost import SparkPost
            from sparkpost.exceptions import SparkPostAPIException

            # Initialize the client
            sp = SparkPost(self.api_key)
            self.logger.info("SparkPost API key validation passed basic checks")
        except ImportError:
            raise ImportError("SparkPost module not installed. Please install it with 'pip install sparkpost'")
        except Exception as e:
            raise ValueError(f"Error initializing SparkPost client: {str(e)}")

    def send_emails(self, email_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Send emails using SparkPost.

        Args:
            email_data: DataFrame containing email data

        Returns:
            Dict with results of the send operation
        """
        self.logger.info(f"Preparing to send {len(email_data)} emails via SparkPost")

        # Add result columns
        email_data["Campaign_ID"] = ""
        email_data["Accepted"] = ""
        email_data["Rejected"] = ""

        # Add preheader if available
        if "Preheader" in email_data.columns:
            email_data['HTML_Content'] = (
                "<div style='display:none; font-size:1px; line-height:1px; max-height:0; max-width:0; opacity:0; overflow:hidden;'>" +
                email_data['Preheader'].fillna("") +
                "</div>" +
                email_data['HTML_Content']
            )

        # Process in batches
        results = {
            "total_sent": 0,
            "total_accepted": 0,
            "total_rejected": 0,
            "errors": []
        }

        try:
            # Import SparkPost here to avoid dependency issues
            from sparkpost import SparkPost
            from sparkpost.exceptions import SparkPostAPIException

            # Initialize SparkPost client
            if not self.api_key or len(self.api_key) < 20:
                raise ValueError("Invalid SparkPost API key. Please provide a valid API key.")

            sp = SparkPost(self.api_key)

            # Generate campaign ID
            campaign_id = f"openengage_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"

            # Process in batches of 100
            batch_size = 100
            for i in range(0, len(email_data), batch_size):
                end_idx = min(i + batch_size, len(email_data))
                batch = email_data.iloc[i:end_idx].copy()

                try:
                    # Prepare recipients
                    recipients = [{"address": {"email": email}} for email in batch["user_email"]]

                    # Prepare email content
                    offers = list(batch["user_email"])
                    email_dict = batch.set_index("user_email").to_dict()

                    # Send emails
                    response = sp.transmissions.send(
                        recipients=recipients,
                        html="{{each offers}}{{if loop_var==address.email}}{{render_dynamic_content(dynamic_html[loop_var])}}{{end}}{{end}}",
                        from_email=self.from_email,
                        subject="{{each offers}}{{if loop_var==address.email}}{{dynamic_subject[loop_var]}}{{end}}{{end}}",
                        track_opens=True,
                        track_clicks=True,
                        campaign=campaign_id,
                        metadata={
                            'source': 'openengage',
                            'batch': f'{i}-{end_idx}'
                        },
                        substitution_data={
                            "offers": offers,
                            "dynamic_html": email_dict["HTML_Content"],
                            "dynamic_subject": email_dict["Subject"]
                        },
                        reply_to=self.reply_to_email
                    )

                    # Process response
                    total_accepted = response.get('total_accepted_recipients', 0)
                    total_rejected = response.get('total_rejected_recipients', 0)

                    # Update batch results
                    batch["Campaign_ID"] = campaign_id
                    batch["Accepted"] = total_accepted
                    batch["Rejected"] = total_rejected

                    # Update email_data with batch results
                    email_data.iloc[i:end_idx] = batch

                    # Update overall results
                    results["total_accepted"] += total_accepted
                    results["total_rejected"] += total_rejected
                    results["total_sent"] += len(batch)

                    self.logger.info(f"Batch {i}-{end_idx}: {total_accepted} accepted, {total_rejected} rejected")

                except SparkPostAPIException as e:
                    error_msg = f"SparkPost API error in batch {i}-{end_idx}: {str(e)}"
                    results["errors"].append(error_msg)
                    self.logger.error(error_msg)

                except Exception as e:
                    error_msg = f"Error sending batch {i}-{end_idx}: {str(e)}"
                    results["errors"].append(error_msg)
                    self.logger.error(error_msg)

        except Exception as e:
            error_msg = f"Fatal error in email sending: {str(e)}"
            results["errors"].append(error_msg)
            self.logger.error(error_msg)

        return results

    def schedule_emails(self, email_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Schedule emails for future delivery using SparkPost.

        Args:
            email_data: DataFrame containing email data with Send_Time column

        Returns:
            Dict with results of the schedule operation
        """
        self.logger.info(f"Preparing to schedule {len(email_data)} emails via SparkPost")

        # Add result columns
        email_data["Campaign_ID"] = ""
        email_data["Accepted"] = ""
        email_data["Rejected"] = ""

        # Convert Send_Time to ISO format if needed
        if "Send_Time" in email_data.columns:
            email_data["ISO_Date_Time"] = pd.to_datetime(email_data["Send_Time"]).dt.strftime('%Y-%m-%dT%H:%M:%S')

        # Process in batches
        results = {
            "total_sent": 0,
            "total_accepted": 0,
            "total_rejected": 0,
            "errors": []
        }

        try:
            # Import SparkPost here to avoid dependency issues
            from sparkpost import SparkPost
            from sparkpost.exceptions import SparkPostAPIException

            # Initialize SparkPost client
            if not self.api_key or len(self.api_key) < 20:
                raise ValueError("Invalid SparkPost API key. Please provide a valid API key.")

            sp = SparkPost(self.api_key)

            # Sort by send time
            email_data = email_data.sort_values(by=["Send_Time"])

            # Generate campaign ID
            campaign_id = f"openengage_scheduled_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"

            # Group by send time and process in batches
            for send_time, group in email_data.groupby("ISO_Date_Time"):
                batch_size = 100
                for i in range(0, len(group), batch_size):
                    end_idx = min(i + batch_size, len(group))
                    batch = group.iloc[i:end_idx].copy()

                    try:
                        # Prepare recipients
                        recipients = [{"address": {"email": email}} for email in batch["user_email"]]

                        # Prepare email content
                        offers = list(batch["user_email"])
                        email_dict = batch.set_index("user_email").to_dict()

                        # Schedule emails
                        response = sp.transmissions.send(
                            recipients=recipients,
                            html="{{each offers}}{{if loop_var==address.email}}{{render_dynamic_content(dynamic_html[loop_var])}}{{end}}{{end}}",
                            from_email=self.from_email,
                            subject="{{each offers}}{{if loop_var==address.email}}{{dynamic_subject[loop_var]}}{{end}}{{end}}",
                            track_opens=True,
                            track_clicks=True,
                            campaign=campaign_id,
                            start_time=batch["ISO_Date_Time"].iloc[0],
                            metadata={
                                'source': 'openengage',
                                'batch': f'{i}-{end_idx}'
                            },
                            substitution_data={
                                "offers": offers,
                                "dynamic_html": email_dict["HTML_Content"],
                                "dynamic_subject": email_dict["Subject"]
                            },
                            reply_to=self.reply_to_email
                        )

                        # Process response
                        total_accepted = response.get('total_accepted_recipients', 0)
                        total_rejected = response.get('total_rejected_recipients', 0)

                        # Update batch results
                        batch["Campaign_ID"] = campaign_id
                        batch["Accepted"] = total_accepted
                        batch["Rejected"] = total_rejected

                        # Update overall results
                        results["total_accepted"] += total_accepted
                        results["total_rejected"] += total_rejected
                        results["total_sent"] += len(batch)

                        self.logger.info(f"Scheduled batch for {send_time}: {total_accepted} accepted, {total_rejected} rejected")

                    except SparkPostAPIException as e:
                        error_msg = f"SparkPost API error scheduling batch for {send_time}: {str(e)}"
                        results["errors"].append(error_msg)
                        self.logger.error(error_msg)

                    except Exception as e:
                        error_msg = f"Error scheduling batch for {send_time}: {str(e)}"
                        results["errors"].append(error_msg)
                        self.logger.error(error_msg)

        except Exception as e:
            error_msg = f"Fatal error in email scheduling: {str(e)}"
            results["errors"].append(error_msg)
            self.logger.error(error_msg)

        return results


class MailmodoSender(EmailSender):
    """Mailmodo implementation of email sender"""

    def __init__(self, api_key: str):
        """Initialize the Mailmodo sender with API key"""
        super().__init__(api_key)
        self.validate_api_key()

    def validate_api_key(self):
        """Validate the Mailmodo API key"""
        if not self.api_key:
            raise ValueError("Mailmodo API key is required")

    def send_emails(self, email_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Send emails using Mailmodo.
        Note: This is a placeholder implementation. Actual Mailmodo API integration needed.
        """
        self.logger.info(f"Preparing to send {len(email_data)} emails via Mailmodo")
        
        # Placeholder implementation
        return {
            "total_sent": len(email_data),
            "total_accepted": len(email_data),
            "total_rejected": 0,
            "errors": [],
            "message": "Mailmodo integration not yet implemented"
        }

    def schedule_emails(self, email_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Schedule emails using Mailmodo.
        Note: This is a placeholder implementation.
        """
        self.logger.info(f"Preparing to schedule {len(email_data)} emails via Mailmodo")
        
        # Placeholder implementation
        return {
            "total_sent": len(email_data),
            "total_accepted": len(email_data),
            "total_rejected": 0,
            "errors": [],
            "message": "Mailmodo scheduling not yet implemented"
        }


class AmazonSESSender(EmailSender):
    """Amazon SES implementation of email sender"""

    def __init__(self, api_key: str):
        """Initialize the Amazon SES sender with API key"""
        super().__init__(api_key)
        self.validate_api_key()

    def validate_api_key(self):
        """Validate the Amazon SES API key"""
        if not self.api_key:
            raise ValueError("Amazon SES API key is required")

    def send_emails(self, email_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Send emails using Amazon SES.
        Note: This is a placeholder implementation. Actual SES API integration needed.
        """
        self.logger.info(f"Preparing to send {len(email_data)} emails via Amazon SES")
        
        # Placeholder implementation
        return {
            "total_sent": len(email_data),
            "total_accepted": len(email_data),
            "total_rejected": 0,
            "errors": [],
            "message": "Amazon SES integration not yet implemented"
        }

    def schedule_emails(self, email_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Schedule emails using Amazon SES.
        Note: This is a placeholder implementation.
        """
        self.logger.info(f"Preparing to schedule {len(email_data)} emails via Amazon SES")
        
        # Placeholder implementation
        return {
            "total_sent": len(email_data),
            "total_accepted": len(email_data),
            "total_rejected": 0,
            "errors": [],
            "message": "Amazon SES scheduling not yet implemented"
        }


def create_email_sender(provider: str, api_key: str) -> EmailSender:
    """
    Create an email sender instance based on the provider.

    Args:
        provider: Name of the email service provider
        api_key: API key for the provider

    Returns:
        EmailSender instance
    """
    if provider.lower() == "sparkpost":
        return SparkPostSender(api_key)
    elif provider.lower() == "mailmodo":
        return MailmodoSender(api_key)
    elif provider.lower() in ["amazon_ses", "amazon ses"]:
        return AmazonSESSender(api_key)
    else:
        raise ValueError(f"Unsupported email provider: {provider}")


def load_sending_configuration() -> Dict[str, Any]:
    """
    Load sending configuration from file.

    Returns:
        Dict containing sending configuration
    """
    config_file = 'data/sending_configuration.json'

    if os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except json.JSONDecodeError:
            return {}

    return {}


def get_esp_api_keys() -> Dict[str, str]:
    """
    Get ESP API keys from environment variables.

    Returns:
        Dict containing API keys for different providers
    """
    load_dotenv()
    return {
        "sparkpost": os.getenv("SPARKPOST_API_KEY", ""),
        "mailmodo": os.getenv("MAILMODO_API_KEY", ""),
        "amazon_ses": os.getenv("AMAZON_SES_API_KEY", ""),
        "twilio": os.getenv("TWILIO_API_KEY", ""),
        "gupshup": os.getenv("GUPSHUP_API_KEY", ""),
        "messagebird": os.getenv("MESSAGEBIRD_API_KEY", "")
    }


def get_active_sending_configuration() -> Optional[Dict[str, Any]]:
    """
    Get the active sending configuration.

    Returns:
        Dict containing active configuration or None if not found
    """
    config = load_sending_configuration()

    # Get current details (active configuration)
    current_details = config.get("current_details", {})

    if current_details:
        return current_details

    # Fallback to first configuration if available
    configurations = config.get("configurations", [])
    if configurations:
        return configurations[0]

    return None


def send_test_email(recipient_email: str, subject: str, html_content: str,
                   provider: str = "SparkPost") -> Dict[str, Any]:
    """
    Send a test email using the specified provider.

    Args:
        recipient_email: Recipient's email address
        subject: Email subject
        html_content: HTML content of the email
        provider: Email service provider

    Returns:
        Dict with results of the send operation
    """
    try:
        # Get API keys
        api_keys = get_esp_api_keys()

        # Get active configuration
        active_config = get_active_sending_configuration()

        if not active_config:
            return {
                "success": False,
                "error": "No active sending configuration found"
            }

        # Get API key for the provider
        api_key = None
        if provider.lower() == "sparkpost":
            api_key = api_keys.get("sparkpost")
        elif provider.lower() == "mailmodo":
            api_key = api_keys.get("mailmodo")
        elif provider.lower() in ["amazon_ses", "amazon ses"]:
            api_key = api_keys.get("amazon_ses")

        if not api_key:
            return {
                "success": False,
                "error": f"No API key found for {provider}"
            }

        # Create email sender
        sender = create_email_sender(provider.lower().replace(" ", "_"), api_key)

        # Set sender details
        sender.set_sender_details(
            active_config.get("sender_name", "OpenEngage Team"),
            active_config.get("sender_email", "<EMAIL>"),
            active_config.get("reply_to_email", "<EMAIL>")
        )

        # Create test email data
        test_data = pd.DataFrame({
            "user_email": [recipient_email],
            "Subject": [subject],
            "HTML_Content": [html_content]
        })

        # Send the email
        results = sender.send_emails(test_data)

        return {
            "success": results["total_accepted"] > 0,
            "results": results,
            "provider": provider
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def send_email_campaign(email_data: pd.DataFrame, provider: str = None) -> Dict[str, Any]:
    """
    Send an email campaign using the configured provider.

    Args:
        email_data: DataFrame containing email campaign data
        provider: Optional provider override

    Returns:
        Dict with campaign results
    """
    try:
        # Get active configuration
        active_config = get_active_sending_configuration()

        if not active_config:
            return {
                "success": False,
                "error": "No active sending configuration found"
            }

        # Use provided provider or get from configuration
        if not provider:
            provider = active_config.get("esp", "SparkPost")

        # Get API keys
        api_keys = get_esp_api_keys()

        # Get API key for the provider
        api_key = None
        if provider.lower() == "sparkpost":
            api_key = api_keys.get("sparkpost")
        elif provider.lower() == "mailmodo":
            api_key = api_keys.get("mailmodo")
        elif provider.lower() in ["amazon_ses", "amazon ses"]:
            api_key = api_keys.get("amazon_ses")

        if not api_key:
            return {
                "success": False,
                "error": f"No API key found for {provider}"
            }

        # Create email sender
        sender = create_email_sender(provider.lower().replace(" ", "_"), api_key)

        # Set sender details
        sender.set_sender_details(
            active_config.get("sender_name", "OpenEngage Team"),
            active_config.get("sender_email", "<EMAIL>"),
            active_config.get("reply_to_email", "<EMAIL>")
        )

        # Send the campaign
        results = sender.send_emails(email_data)

        return {
            "success": True,
            "campaign_results": results,
            "provider": provider,
            "total_emails": len(email_data)
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def schedule_email_campaign(email_data: pd.DataFrame, provider: str = None) -> Dict[str, Any]:
    """
    Schedule an email campaign for future delivery.

    Args:
        email_data: DataFrame containing email campaign data with Send_Time column
        provider: Optional provider override

    Returns:
        Dict with scheduling results
    """
    try:
        # Get active configuration
        active_config = get_active_sending_configuration()

        if not active_config:
            return {
                "success": False,
                "error": "No active sending configuration found"
            }

        # Use provided provider or get from configuration
        if not provider:
            provider = active_config.get("esp", "SparkPost")

        # Get API keys
        api_keys = get_esp_api_keys()

        # Get API key for the provider
        api_key = None
        if provider.lower() == "sparkpost":
            api_key = api_keys.get("sparkpost")
        elif provider.lower() == "mailmodo":
            api_key = api_keys.get("mailmodo")
        elif provider.lower() in ["amazon_ses", "amazon ses"]:
            api_key = api_keys.get("amazon_ses")

        if not api_key:
            return {
                "success": False,
                "error": f"No API key found for {provider}"
            }

        # Create email sender
        sender = create_email_sender(provider.lower().replace(" ", "_"), api_key)

        # Set sender details
        sender.set_sender_details(
            active_config.get("sender_name", "OpenEngage Team"),
            active_config.get("sender_email", "<EMAIL>"),
            active_config.get("reply_to_email", "<EMAIL>")
        )

        # Schedule the campaign
        results = sender.schedule_emails(email_data)

        return {
            "success": True,
            "scheduling_results": results,
            "provider": provider,
            "total_emails": len(email_data)
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def save_campaign_results(email_data: pd.DataFrame, results: Dict[str, Any],
                         campaign_name: str = None) -> str:
    """
    Save email campaign results to file.

    Args:
        email_data: DataFrame with campaign data and results
        results: Campaign results dictionary
        campaign_name: Optional campaign name

    Returns:
        Path to saved file
    """
    # Create results directory if it doesn't exist
    results_dir = "data/campaign_results"
    os.makedirs(results_dir, exist_ok=True)

    # Generate filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if campaign_name:
        filename = f"{campaign_name}_{timestamp}.csv"
    else:
        filename = f"email_campaign_{timestamp}.csv"

    filepath = os.path.join(results_dir, filename)

    # Add campaign metadata to the DataFrame
    email_data_copy = email_data.copy()
    email_data_copy['Campaign_Timestamp'] = timestamp
    email_data_copy['Total_Accepted'] = results.get('total_accepted', 0)
    email_data_copy['Total_Rejected'] = results.get('total_rejected', 0)

    # Save campaign data
    email_data_copy.to_csv(filepath, index=False)

    return filepath


def validate_email_data(email_data: pd.DataFrame) -> Dict[str, Any]:
    """
    Validate email campaign data.

    Args:
        email_data: DataFrame to validate

    Returns:
        Dict with validation results
    """
    errors = []
    warnings = []

    # Check required columns
    required_columns = ['user_email', 'Subject', 'HTML_Content']
    missing_columns = [col for col in required_columns if col not in email_data.columns]

    if missing_columns:
        errors.append(f"Missing required columns: {missing_columns}")

    # Check for empty data
    if email_data.empty:
        errors.append("Email data is empty")

    # Validate email addresses
    if 'user_email' in email_data.columns:
        invalid_emails = []
        for idx, email in email_data['user_email'].items():
            if not isinstance(email, str) or '@' not in email:
                invalid_emails.append(f"Row {idx}: {email}")

        if invalid_emails:
            warnings.append(f"Invalid email addresses found: {invalid_emails[:5]}")  # Show first 5

    # Check for empty subjects or content
    if 'Subject' in email_data.columns:
        empty_subjects = email_data['Subject'].isna().sum()
        if empty_subjects > 0:
            warnings.append(f"{empty_subjects} emails have empty subjects")

    if 'HTML_Content' in email_data.columns:
        empty_content = email_data['HTML_Content'].isna().sum()
        if empty_content > 0:
            warnings.append(f"{empty_content} emails have empty content")

    return {
        "valid": len(errors) == 0,
        "errors": errors,
        "warnings": warnings,
        "total_emails": len(email_data)
    }


def get_campaign_statistics(results: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate campaign statistics from results.

    Args:
        results: Campaign results dictionary

    Returns:
        Dict with campaign statistics
    """
    total_sent = results.get('total_sent', 0)
    total_accepted = results.get('total_accepted', 0)
    total_rejected = results.get('total_rejected', 0)

    # Calculate rates
    acceptance_rate = (total_accepted / total_sent * 100) if total_sent > 0 else 0
    rejection_rate = (total_rejected / total_sent * 100) if total_sent > 0 else 0

    return {
        "total_emails_sent": total_sent,
        "total_accepted": total_accepted,
        "total_rejected": total_rejected,
        "acceptance_rate": round(acceptance_rate, 2),
        "rejection_rate": round(rejection_rate, 2),
        "errors_count": len(results.get('errors', [])),
        "errors": results.get('errors', [])
    }


def test_esp_connection(provider: str, api_key: str = None) -> Dict[str, Any]:
    """
    Test connection to an email service provider.

    Args:
        provider: Email service provider name
        api_key: API key (optional, will use env var if not provided)

    Returns:
        Dict with connection test results
    """
    try:
        # Get API key if not provided
        if not api_key:
            api_keys = get_esp_api_keys()
            api_key = api_keys.get(provider.lower(), "")

        if not api_key:
            return {
                "success": False,
                "provider": provider,
                "error": f"No API key found for {provider}"
            }

        # Create sender instance
        sender = create_email_sender(provider.lower().replace(" ", "_"), api_key)

        # For SparkPost, we can test the connection
        if provider.lower() == "sparkpost":
            try:
                from sparkpost import SparkPost
                sp = SparkPost(api_key)
                # Try to get sending domains (simple API call)
                response = sp.sending_domains.list()
                return {
                    "success": True,
                    "provider": provider,
                    "message": f"Connection successful. Found {len(response)} sending domains."
                }
            except Exception as e:
                return {
                    "success": False,
                    "provider": provider,
                    "error": f"Connection failed: {str(e)}"
                }
        else:
            # For other providers, just validate the sender creation
            return {
                "success": True,
                "provider": provider,
                "message": f"Sender instance created successfully for {provider}"
            }

    except Exception as e:
        return {
            "success": False,
            "provider": provider,
            "error": str(e)
        }


# Main execution functions for external use

def execute_email_campaign(email_data: pd.DataFrame, provider: str = None,
                          validate_data: bool = True, save_results: bool = True,
                          campaign_name: str = None) -> Dict[str, Any]:
    """
    Execute a complete email campaign with validation and result saving.

    Args:
        email_data: DataFrame containing email campaign data
        provider: Email service provider
        validate_data: Whether to validate data before sending
        save_results: Whether to save results to file
        campaign_name: Optional campaign name

    Returns:
        Dict containing campaign execution results
    """
    try:
        # Validate data if requested
        validation_results = None
        if validate_data:
            validation_results = validate_email_data(email_data)
            if not validation_results["valid"]:
                return {
                    "success": False,
                    "error": "Data validation failed",
                    "validation_results": validation_results
                }

        # Send campaign
        campaign_results = send_email_campaign(email_data, provider)

        if not campaign_results["success"]:
            return campaign_results

        # Generate statistics
        statistics = get_campaign_statistics(campaign_results["campaign_results"])

        # Save results if requested
        saved_file = None
        if save_results:
            saved_file = save_campaign_results(
                email_data,
                campaign_results["campaign_results"],
                campaign_name
            )

        return {
            "success": True,
            "campaign_results": campaign_results["campaign_results"],
            "statistics": statistics,
            "validation_results": validation_results,
            "saved_file": saved_file,
            "provider": campaign_results["provider"]
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }
