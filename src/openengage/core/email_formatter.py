"""
Email formatting utilities for OpenEngage.
"""
import re
import html
import logging
import json
import os
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
# Import unsubscribe_utils or define a placeholder function
try:
    from ..utils.unsubscribe_utils import create_unsubscribe_link
except ImportError:
    # Define a placeholder function if the module is not available
    def create_unsubscribe_link(email):
        return f"#unsubscribe-{email}" if email else "#"

# Import brand guidelines from utils
from utils.file_utils import load_brand_guidelines as utils_load_brand_guidelines

# Configure logging with file output
import datetime

def setup_email_formatter_logging():
    """Setup comprehensive logging for email_formatter module with file storage."""
    # Create logs directory if it doesn't exist
    logs_dir = 'data/logs'
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir, exist_ok=True)

    # Create a unique log file name with timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"email_formatter_{timestamp}.log"
    log_filepath = os.path.join(logs_dir, log_filename)

    # Configure logger
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)

    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create file handler for detailed logging
    file_handler = logging.FileHandler(log_filepath, mode='a', encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)

    # Create console handler for immediate feedback
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Create detailed formatter for file
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Create simple formatter for console
    console_formatter = logging.Formatter(
        '%(levelname)s - %(funcName)s - %(message)s'
    )

    file_handler.setFormatter(file_formatter)
    console_handler.setFormatter(console_formatter)

    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    # Log the initialization
    logger.info(f"Email formatter logging initialized. Log file: {log_filepath}")

    return logger

# Initialize logging
logger = setup_email_formatter_logging()

product_replaced = 0

def load_template_cta(template_name):
    """
    Load CTA text for a specific template from the JSON file.

    This function loads custom CTA text from the template_ctas.json file based on the
    template name. If the template is found, it returns the custom CTA text, otherwise
    returns None to allow fallback to default CTA text.

    Args:
        template_name (str): Name of the template to get CTA for

    Returns:
        str: CTA text if found, None otherwise to use default CTA
    """
    if not template_name:
        print("DEBUG: No template name provided for CTA lookup")
        return None

    try:
        cta_file_path = 'data/templates/template_ctas.json'

        if not os.path.exists(cta_file_path):
            print(f"DEBUG: CTA file not found at {cta_file_path}")
            return None

        with open(cta_file_path, 'r', encoding='utf-8') as f:
            ctas = json.load(f)

        # Get CTA text for the specific template
        cta_text = ctas.get(template_name)

        if cta_text:
            # Clean up the CTA text - remove any quotes and strip whitespace
            cta_text = cta_text.strip().strip('"').strip("'")
            print(f"DEBUG: Found CTA for template '{template_name}': {cta_text}")
            return cta_text
        else:
            print(f"DEBUG: No CTA found for template '{template_name}' in template_ctas.json")
            return None

    except json.JSONDecodeError as e:
        print(f"DEBUG: JSON decode error loading CTA file: {str(e)}")
        return None
    except Exception as e:
        print(f"DEBUG: Error loading CTA for template '{template_name}': {str(e)}")
        return None

def get_cta_text_for_template(template_name, default_cta='Learn More'):
    """
    Get CTA text for a template with fallback to default.

    This is a helper function that centralizes the CTA text retrieval logic
    used throughout the email formatting process. It tries exact match first,
    then pattern matching if exact match fails.

    Args:
        template_name (str): Name of the template to get CTA for
        default_cta (str): Default CTA text to use if template not found

    Returns:
        str: CTA text (either custom from JSON or default)
    """
    if not template_name:
        print(f"DEBUG: No template name provided, using default CTA: '{default_cta}'")
        return default_cta

    # Try exact match first
    custom_cta = load_template_cta(template_name)
    if custom_cta:
        print(f"DEBUG: Using custom CTA '{custom_cta}' for template '{template_name}'")
        return custom_cta

    # If exact match fails, try pattern matching
    try:
        cta_file_path = 'data/templates/template_ctas.json'
        if os.path.exists(cta_file_path):
            with open(cta_file_path, 'r', encoding='utf-8') as f:
                ctas = json.load(f)

            # Extract key parts from template_name for pattern matching
            template_parts = template_name.lower().replace('_', ' ').split()

            # Look for templates that contain similar patterns
            best_match = None
            max_matches = 0

            for cta_template_name, cta_text in ctas.items():
                cta_parts = cta_template_name.lower().replace('_', ' ').split()

                # Count matching parts
                matches = sum(1 for part in template_parts if any(part in cta_part for cta_part in cta_parts))

                if matches > max_matches and matches >= 2:  # Require at least 2 matching parts
                    max_matches = matches
                    best_match = cta_text
                    print(f"DEBUG: Found pattern match '{cta_template_name}' with {matches} matching parts")

            if best_match:
                best_match = best_match.strip().strip('"').strip("'")
                print(f"DEBUG: Using pattern-matched CTA '{best_match}' for template '{template_name}'")
                return best_match

    except Exception as e:
        print(f"DEBUG: Error in pattern matching for template '{template_name}': {str(e)}")

    print(f"DEBUG: No custom CTA found for template '{template_name}', using default: '{default_cta}'")
    return default_cta

def load_brand_guidelines(organization_url=None):
    """
    Load brand guidelines from file.

    Args:
        organization_url (str, optional): The organization URL to filter by.

    Returns:
        dict: The loaded brand guidelines or default guidelines if not found.
    """
    # Use the imported function from utils.file_utils
    if organization_url:
        # Clean up the organization URL to handle common variations
        # Remove trailing slashes, 'www.' prefix, and normalize to lowercase for comparison
        clean_org_url = organization_url.rstrip('/').lower()
        if clean_org_url.startswith('www.'):
            clean_org_url = clean_org_url[4:]  # Remove 'www.' prefix
        elif clean_org_url.startswith('http://www.'):
            clean_org_url = clean_org_url[11:]  # Remove 'http://www.' prefix
        elif clean_org_url.startswith('https://www.'):
            clean_org_url = clean_org_url[12:]  # Remove 'https://www.' prefix
        elif clean_org_url.startswith('http://'):
            clean_org_url = clean_org_url[7:]  # Remove 'http://' prefix
        elif clean_org_url.startswith('https://'):
            clean_org_url = clean_org_url[8:]  # Remove 'https://' prefix

        # Log the cleaned URL for debugging
        logger.debug(f"Looking for brand guidelines with cleaned URL: {clean_org_url}")

        # Get all guidelines
        all_guidelines = utils_load_brand_guidelines()

        # Check if we got a dictionary of organizations
        if isinstance(all_guidelines, dict):
            # Try exact match first with the original URL
            if organization_url in all_guidelines:
                logger.debug(f"Found exact match for {organization_url}")
                return all_guidelines[organization_url]

            # Try with cleaned URL
            for url, guidelines in all_guidelines.items():
                # Clean up the URL from the guidelines for comparison
                clean_url = url.rstrip('/').lower()
                if clean_url.startswith('www.'):
                    clean_url = clean_url[4:]
                elif clean_url.startswith('http://www.'):
                    clean_url = clean_url[11:]
                elif clean_url.startswith('https://www.'):
                    clean_url = clean_url[12:]
                elif clean_url.startswith('http://'):
                    clean_url = clean_url[7:]
                elif clean_url.startswith('https://'):
                    clean_url = clean_url[8:]

                # Compare cleaned URLs
                if clean_url == clean_org_url:
                    logger.debug(f"Found match with cleaned URL: {url}")
                    return guidelines

                # Try partial match (e.g., domain only)
                if clean_org_url in clean_url or clean_url in clean_org_url:
                    logger.debug(f"Found partial match: {url}")
                    return guidelines

            # If no match found, return default guidelines
            logger.warning(f"No brand guidelines found for {organization_url}. Using default guidelines.")
            if 'default_organization' in all_guidelines:
                return all_guidelines['default_organization']
            elif len(all_guidelines) > 0:
                # Return the first organization's guidelines as fallback
                logger.debug("Using first available organization's guidelines as fallback")
                return next(iter(all_guidelines.values()))
            else:
                # Empty dictionary, return empty guidelines
                logger.warning("No brand guidelines found at all. Using empty guidelines.")
                return {}
        else:
            # If it's a single organization's guidelines, return it
            return all_guidelines
    else:
        # If no organization URL provided, get default guidelines
        all_guidelines = utils_load_brand_guidelines()

        # Return the first organization's guidelines or default
        if isinstance(all_guidelines, dict):
            if 'default_organization' in all_guidelines:
                return all_guidelines['default_organization']
            elif len(all_guidelines) > 0:
                # Return the first organization's guidelines
                return next(iter(all_guidelines.values()))
            else:
                # Empty dictionary, return empty guidelines
                logger.warning("No brand guidelines found. Using default guidelines.")
                return {}

def text_to_html(
    email_content: Dict[str, Any],
    product_url: Optional[str] = None,
    product_name: Optional[str] = None,
    communication_settings: Optional[Dict[str, Any]] = None,
    recipient_email: Optional[str] = None,
    recipient_first_name: Optional[str] = None,
    brand_guidelines: Optional[Dict[str, Any]] = None,
    template_name: Optional[str] = None
) -> str:
    """
    Convert plain text email content to HTML format with brand styling and CTA integration.

    This function processes plain text email content and converts it to properly formatted
    HTML with brand guidelines, UTM tracking, personalization, and custom CTA buttons.
    If template_name is provided, it will load custom CTA text from template_ctas.json.

    Args:
        email_content: Dictionary containing email content with 'subject' and 'content' keys
        product_url: URL of the product for linking (optional)
        product_name: Name of the product for link text (optional)
        communication_settings: Dictionary containing UTM parameters and sender info (optional)
        recipient_email: Email address of the recipient for unsubscribe link (optional)
        recipient_first_name: First name of the recipient for personalized greeting (optional)
        brand_guidelines: Dictionary containing brand guidelines for styling (optional)
        template_name: Name of the template for CTA lookup in template_ctas.json (optional)

    Returns:
        HTML formatted email content with embedded styling and CTA buttons
    """
    if not email_content or not isinstance(email_content, dict):
        return ""

    # Extract content
    content = email_content.get('content', '')

    if not content:
        return ""

    # Remove any "Body:" or "Email Body:" prefixes
    content = re.sub(r'^\s*(?:Body|Email Body)\s*:\s*', '', content)

    # Check if content already contains HTML tags
    contains_html = re.search(r'<[a-z]+[^>]*>', content, re.IGNORECASE) is not None

    # Only escape HTML special characters if the content doesn't already contain HTML
    if not contains_html:
        content = html.escape(content)

    # Get sender name for salutation replacement
    sender_name = None
    organization_url = None
    if communication_settings:
        sender_name = communication_settings.get('sender_name', 'OpenEngage Team')
        organization_url = communication_settings.get('organization_url')

        # Try to get organization URL from template_context if available
        if not organization_url and 'template_context' in communication_settings:
            template_context = communication_settings.get('template_context', {})
            if 'base_template' in template_context:
                product_data = template_context.get('base_template', {}).get('product_data', {})
                organization_url = product_data.get('Company_URL') or product_data.get('organization_url')

                # Store the organization URL in communication_settings for later use
                if organization_url:
                    communication_settings['organization_url'] = organization_url

        # Replace any "Best regards" or similar closing with sender name
        content = re.sub(
            r'(?i)(\n\s*)(best\s+regards|regards|sincerely|yours\s+truly|best|thanks|thank\s+you)[,.]?\s*$',
            r'\1Best regards,<br>' + sender_name,
            content
        )

    # Extract recipient's first name from email if available
    if recipient_first_name is None:
        if recipient_email:
            # Extract name from email (e.g., '<EMAIL>' -> 'John')
            try:
                local_part = recipient_email.split('@')[0]
                # Try to get first name from the local part
                if '.' in local_part:
                    first_name = local_part.split('.')[0]
                else:
                    # If no period, use the whole local part or up to any non-alphabetic character
                    match = re.search(r'^([a-zA-Z]+)', local_part)
                    if match:
                        first_name = match.group(1)
                    else:
                        first_name = local_part

                # Capitalize the first name properly
                recipient_first_name = first_name.capitalize()
            except Exception:
                # If any error occurs, proceed without the first name
                pass

    # Use provided brand guidelines or load from file based on organization URL
    if brand_guidelines is None:
        brand_guidelines = load_brand_guidelines(organization_url)

    # Process the content
    paragraphs = process_paragraphs(content, sender_name, communication_settings, product_name, product_url, recipient_first_name, brand_guidelines)

    # Get company name for footer
    company_name = "OpenEngage"
    if communication_settings and 'sender_name' in communication_settings:
        company_name = communication_settings.get('sender_name')

    # Create the full HTML email (without subject in the body)
    html_email = create_html_email_template(paragraphs, company_name, product_url, recipient_email, brand_guidelines, template_name)

    return html_email

def process_paragraphs(content: str, sender_name: str = None, communication_settings: Dict[str, Any] = None, product_name: str = None, product_url: str = None, recipient_first_name: str = None, brand_guidelines: Dict[str, Any] = None) -> str:
    """
    Process text content into HTML paragraphs.

    Args:
        content: Plain text content
        sender_name: Name of the sender for salutation replacement
        communication_settings: Dictionary containing UTM parameters
        product_name: Name of the product for link text
        product_url: URL of the product to link the product name
        recipient_first_name: First name of the recipient for personalized greeting
        brand_guidelines: Dictionary containing brand guidelines

    Returns:
        HTML formatted paragraphs
    """
    # Check if content starts with a greeting
    greeting_match = re.match(r'^\s*(Hi|Hello|Dear|Hey)\s*(?:[^,\n]*)?([,.])?\s*', content, re.IGNORECASE)

    if recipient_first_name:
        # If there's an existing greeting, replace it with personalized one
        if greeting_match:
            greeting_word = greeting_match.group(1)  # e.g., 'Hi', 'Hello', etc.
            punctuation = greeting_match.group(2) or ','  # Use existing punctuation or default to comma
            new_greeting = f"{greeting_word} {recipient_first_name}{punctuation}\n\n"
            content = content[greeting_match.end():].strip()
            content = new_greeting + content
        else:
            # No greeting found, add one
            content = f"Hi {recipient_first_name},\n\n{content}"
    else:
        # No recipient name available
        if not greeting_match:
            # No greeting found, add generic one
            content = f"Hi,\n\n{content}"

    # We'll process product names and markdown links at the paragraph level
    # Reset the flag to allow product name replacement
    global product_replaced
    product_replaced = 0
    # Process salutation line breaks before splitting into paragraphs
    # Look for the signature pattern like "Best regards,\nSender Name" and ensure proper HTML formatting
    content = re.sub(
        r'Best regards,\s*\n\s*([^\n]+)$',
        r'Best regards,<br>\1',
        content,
        flags=re.IGNORECASE
    )

    # Split content into paragraphs
    logger.debug("Splitting content into paragraphs")
    count_escaped_double_newline = content.count('\\n\\n')   # counts literal \n\n
    count_actual_double_newline  = content.count('\n\n')      # counts real newline-newline
    count_escaped_newline        = content.count('\\n')      # counts literal \n
    count_actual_newline         = content.count('\n')        # counts real newlines

    # If content has no line breaks, intelligently create paragraph breaks
    if count_escaped_double_newline<=1 and count_actual_double_newline<=1 and count_escaped_newline<=2 and count_actual_newline<=2:
        logger.debug("Creating paragraph breaks")
        content=content.replace(",", ",\n\n",1).replace(". ", ".\n\n")

    logger.debug(f"  Escaped double newline: {count_escaped_double_newline}")
    logger.debug(f"  Actual double newline: {count_actual_double_newline}")
    logger.debug(f"  Escaped newline: {count_escaped_newline}")
    logger.debug(f"  Actual newline: {count_actual_newline}")

    # First, handle escaped newlines by converting them to actual newlines
    if "\\n\\n" in content:
        logger.debug("1. Converting escaped double newlines to actual newlines")
        content = content.replace("\\n\\n", "\n\n")
    elif "\\n" in content:
        logger.debug("2. Converting escaped single newlines to actual newlines")
        content = content.replace("\\n", "\n")

    if "\n" in content:
        content=content.replace("\n","\n\n")
    
    if "\n\n\n\n\n\n\n\n" in content:
        content=content.replace("\n\n\n\n\n\n\n\n","\n\n")

    if "\n\n\n\n" in content:
        content=content.replace("\n\n\n\n","\n\n")

    # Now split content into paragraphs
    if "\n\n" in content:
        logger.debug("3. Splitting content into paragraphs with double newline")
        paragraphs = content.split('\n\n')
    else:
        logger.debug("4. Splitting content into paragraphs with single newline")
        paragraphs = content.split('\n')
    logger.debug(f"Split content into {len(paragraphs)} paragraphs")
    logger.debug("Final processed content:")
    logger.debug(repr(content))  # Use repr to show actual characters
    logger.debug("Paragraphs:")
    for i, para in enumerate(paragraphs):
        logger.debug(f"  Paragraph {i+1}: {repr(para[:100])}{'...' if len(para) > 100 else ''}")

    html_paragraphs = []
    for i, para in enumerate(paragraphs):
        logger.debug(f"Processing paragraph {i+1}/{len(paragraphs)}")
        if not para.strip():
            logger.debug(f"Skipping empty paragraph {i+1}")
            continue

        # Handle line breaks within paragraphs (especially for signatures)
        para = para.replace('\n', '<br>')

        # Check if paragraph contains multiple lines with list markers
        list_lines = re.findall(r'^\s*[\*\-\u2022]\s+(.+)$', para, re.MULTILINE)

        # If we have list items, format as a list
        if list_lines and len(list_lines) > 0:
            list_html = '<ul style="margin: 10px 0; padding-left: 20px;">\n'
            for item_text in list_lines:
                # Process URLs in the item
                processed_item = process_urls_in_text(item_text.strip(), communication_settings, product_name, brand_guidelines)
                # Process other formatting
                formatted_item = process_inline_formatting(processed_item)
                list_html += f'  <li style="margin-bottom: 5px;">{formatted_item}</li>\n'
            list_html += '</ul>'
            html_paragraphs.append(list_html)

        # Check if it's a numbered list
        elif re.match(r'^\s*\d+\.\s', para):
            # Convert to ordered list
            items = re.split(r'\n\s*\d+\.\s', para)
            items = [item for item in items if item.strip()]

            if items:
                list_html = '<ol style="margin: 10px 0; padding-left: 20px;">\n'
                for item in items:
                    # Process URLs in the text
                    processed_text = process_urls_in_text(item.strip(), communication_settings, product_name, brand_guidelines)
                    # Process other formatting
                    formatted_item = process_inline_formatting(processed_text)
                    list_html += f'  <li style="margin-bottom: 5px;">{formatted_item}</li>\n'
                list_html += '</ol>'
                html_paragraphs.append(list_html)

        # Check if it's a salutation (usually the last paragraph)
        elif i == len(paragraphs) - 1 and re.search(r'(?:regards|sincerely|cheers|best|thanks|thank you)', para.lower()):
            # Replace generic signatures with the sender name if provided
            if sender_name:
                # Look for common signature patterns
                signature_pattern = r'(?:Best regards|Regards|Sincerely|Best|Cheers|Thanks|Thank you)[,\s]*\n?(?:The .* Team|.* Team|Team|Support Team)'

                # Check if signature exists and doesn't already contain sender name
                if re.search(signature_pattern, para, re.IGNORECASE) and not re.search(re.escape(sender_name), para, re.IGNORECASE):
                    # Add sender name to signature
                    para = re.sub(
                        signature_pattern,
                        f'Best regards,<br>{sender_name}',
                        para,
                        flags=re.IGNORECASE
                    )
                # If signature doesn't exist or already has sender name, just keep it as is
                elif not re.search(signature_pattern, para, re.IGNORECASE):
                    para = f'Best regards,<br>{sender_name}'

            # Process inline formatting
            formatted_para = process_inline_formatting(para)
            html_paragraphs.append(f'<p style="margin: 10px 0; line-height: 1.5;">{formatted_para}</p>')

        # Regular paragraph
        else:
            # First, check if this paragraph contains the product name and it's the first occurrence
            if product_name and product_url:
                # Add UTM parameters to the product URL
                product_url_with_utm = add_utm_parameters(product_url, communication_settings) if communication_settings else product_url

                # Default link color
                link_color = "#0366d6"

                # Apply brand guidelines if available
                if brand_guidelines:
                    # Use primary color for links
                    link_color = brand_guidelines.get("primary_color") or link_color

                # Create linked version of the product name
                linked_product_name = f'<a href="{product_url_with_utm}" style="color: {link_color}; text-decoration: none;">{product_name}</a>'

                # Replace product name with link if it exists in the paragraph
                if product_name in para:
                    para = para.replace(product_name, linked_product_name, 1)
            
            # IMPORTANT: First process markdown links - this must happen before any other processing
            # to prevent interference with link text
            if '[' in para and '](' in para:
                para = process_markdown_links(para, communication_settings, brand_guidelines)

            # Only process the product name if no HTML links exist in the paragraph already
            # This prevents product replacement from breaking existing links
            if '<a href=' not in para and product_name and product_url and product_replaced == 0:
                para = process_product_name(para, product_name, product_url, communication_settings, brand_guidelines)

            # Only process plain URLs if they're not already part of HTML links
            if '<a href=' not in para:
                para = process_urls_in_text(para, communication_settings, product_name, brand_guidelines)

            # Last: Process basic formatting (bold, italic)
            # We've already protected URLs by converting them to HTML links
            formatted_para = process_inline_formatting(para)

            html_paragraphs.append(f'<p style="margin: 10px 0; line-height: 1.5;">{formatted_para}</p>')

    return '\n'.join(html_paragraphs)

def process_inline_formatting(text: str) -> str:
    """
    Process inline text formatting like bold, italic, and links.

    Args:
        text: Plain text content

    Returns:
        HTML formatted text with inline styling
    """
    if not text:
        return text
        
    try:
        # Process bold text (surrounded by ** or __)
        text = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', text)
        text = re.sub(r'__(.*?)__', r'<strong>\1</strong>', text)
        
        # Process italic text (surrounded by * or _)
        text = re.sub(r'\*(.*?)\*', r'<em>\1</em>', text)
        
        # Special case for underscores to avoid matching in URLs
        # Only convert _text_ to <em>text</em> if not part of a URL
        
        # This regex looks for text surrounded by underscores, but not as part of a URL or email
        pattern = r'(?<!\w)_((?!https?://)[^_]+?)_(?!\w)'  # Don't match _part_ of words like email_address or URLs
        text = re.sub(pattern, r'<em>\1</em>', text)
        
        return text
    except Exception as e:
        logger.error(f"Error in process_inline_formatting: {str(e)}")
        # Return the original text if there's an error
        return text

def process_markdown_links(text: str, communication_settings: Dict[str, Any] = None, brand_guidelines: Dict[str, Any] = None) -> str:
    """
    Process markdown style links [text](url) and add UTM parameters.
    Preserves the exact link text and only modifies the URL.

    Args:
        text: Text content that may contain markdown links
        communication_settings: Dictionary containing UTM parameters
        brand_guidelines: Dictionary containing brand guidelines

    Returns:
        Text with processed links
    """
    if not text:
        return text
        
    try:
        # Default link color (use brand color if available)
        link_color = "#0366d6"
        if brand_guidelines and brand_guidelines.get("primary_color"):
            link_color = brand_guidelines.get("primary_color")
            
        # This regex pattern matches markdown links in format [text](url)
        # It carefully captures the text and URL separately
        pattern = r'\[(.*?)\]\((https?://[^\)]+|[^\)]+)\)'
        
        def process_link(match):
            try:
                # Extract the link text and URL
                link_text = match.group(1).strip()  # Text inside []
                url = match.group(2).strip()         # URL inside ()
                
                # Ensure URL has proper scheme
                if not url.startswith('http://') and not url.startswith('https://'):
                    url = 'https://' + url
                    
                # Process the URL if communication settings are provided
                if communication_settings:
                    url = add_utm_parameters(url, communication_settings)
                
                # Create HTML link - IMPORTANT: preserve original link text
                return f'<a href="{url}" style="color: {link_color}; text-decoration: none;">{link_text}</a>'
            except Exception as e:
                logger.error(f"Error processing link: {e}")
                # Return original markdown if there's an error
                return match.group(0)
        
        # Process all markdown links in the text
        processed_text = re.sub(pattern, process_link, text)
        return processed_text
        
    except Exception as e:
        logger.error(f"Error in process_markdown_links: {str(e)}")
        # Return original text if there's an error
        return text

def process_urls_in_text(text: str, communication_settings: Dict[str, Any] = None, product_name: str = None, brand_guidelines: Dict[str, Any] = None) -> str:
    """
    Process plain URLs in text and add UTM parameters if needed.

    Args:
        text: Text content that may contain URLs
        communication_settings: Dictionary containing UTM parameters
        product_name: Name of the product to use in link text
        brand_guidelines: Dictionary containing brand guidelines

    Returns:
        Text with processed URLs
    """
    if not communication_settings:
        return text

    # Default link color
    link_color = "#0366d6"

    # Apply brand guidelines if available
    if brand_guidelines:
        # Use primary color for links
        link_color = brand_guidelines.get("primary_color") or link_color

    # Find URLs in the text that are not already part of an HTML link
    url_pattern = r'(?<!href=")(https?://[^\s<>"]+)'

    def replace_url(match):
        url = match.group(1)
        # Add UTM parameters
        new_url = add_utm_parameters(url, communication_settings)

        # Create a better link text
        if product_name:
            link_text = f"Learn more about {product_name}"
        else:
            # Extract domain name for cleaner link text
            domain_match = re.search(r'https?://(?:www\.)?([^/]+)', url)
            if domain_match:
                link_text = f"Learn more on {domain_match.group(1)}"
            else:
                link_text = "Learn more"

        # Create HTML link with better text
        return f'<a href="{new_url}" style="color: {link_color}; text-decoration: none;">{link_text}</a>'

    # Replace URLs with HTML links with UTM parameters
    processed_text = re.sub(url_pattern, replace_url, text)

    return processed_text

def add_utm_parameters(url: str, communication_settings: Dict[str, Any]) -> str:
    """
    Add UTM parameters to a URL based on communication settings.

    Args:
        url: The URL to modify
        communication_settings: Dictionary containing UTM parameters

    Returns:
        URL with added UTM parameters
    """
    # Guard against malformed URLs
    if not url or '?' in url and url.split('?')[1].endswith('; text-decoration: none;'):  # Already has UTM appended incorrectly
        logger.warning(f"URL appears malformed: {url}")
        # Fix malformed URL by extracting the base part
        url = url.split('?')[0]
    
    # Add http:// prefix if URL doesn't have a scheme
    if not url.startswith('http://') and not url.startswith('https://'):
        url = 'https://' + url
    
    try:
        # Parse the URL
        parsed_url = urlparse(url)
        
        # Get existing query parameters
        query_params = parse_qs(parsed_url.query)
        
        # Add UTM parameters if they exist in communication_settings
        utm_params = {
            'utm_source': communication_settings.get('utm_source', 'email'),
            'utm_medium': communication_settings.get('utm_medium', 'Email'),  # Capitalized to match UI
            'utm_campaign': communication_settings.get('utm_campaign', 'product_launch'),
            'utm_content': communication_settings.get('utm_content', 'initial')
        }
        
        # Update query parameters - replace space with underscores
        for key, value in utm_params.items():
            # Clean up UTM values - replace spaces with underscores and remove special chars
            if value:
                clean_value = re.sub(r'[^a-zA-Z0-9_-]', '_', value)
                query_params[key] = [clean_value]
        
        # Build the new query string
        new_query = urlencode(query_params, doseq=True)
        
        # Reconstruct the URL
        new_url = urlunparse((
            parsed_url.scheme,
            parsed_url.netloc,
            parsed_url.path,
            parsed_url.params,
            new_query,
            parsed_url.fragment
        ))
        
        return new_url
    except Exception as e:
        logger.error(f"Error adding UTM parameters to URL {url}: {str(e)}")
        return url  # Return original URL if any error occurs

def create_cta_button(cta_text: str, url: str, brand_guidelines: Dict[str, Any] = None) -> str:
    """
    Create an HTML CTA button using brand guidelines.

    Args:
        cta_text: Text for the CTA button
        url: URL for the button
        brand_guidelines: Dictionary containing brand guidelines

    Returns:
        HTML for a styled CTA button
    """
    # Default values
    bg_color = "#2674ED"
    text_color = "#FFFFFF"  # Always use white text for buttons
    border_radius = "4px"
    padding = "12px 24px"
    font_family = "Arial, sans-serif"
    font_size = "16px"
    font_weight = "bold"
    using_gradient = False
    gradient_css = ""

    # Apply brand guidelines if available
    if brand_guidelines:
        # Get button color based on the selected type
        button_color_type = brand_guidelines.get("button_color_type", "Primary Color")

        # Check if we're using a gradient
        if button_color_type == "Custom Color":
            bg_color = brand_guidelines.get("button_custom_color", "#000000")
        elif button_color_type == "Gradient" and brand_guidelines.get("has_gradient", False):
            using_gradient = True
            gradient_colors = brand_guidelines.get("gradient_colors", "")
            gradient_direction = brand_guidelines.get("gradient_direction", "to right")

            # Extract colors for visualization
            import re
            hex_colors = re.findall(r'#[0-9a-fA-F]{3,8}', str(gradient_colors))
            if hex_colors and len(hex_colors) >= 2:
                gradient_css = f"linear-gradient({gradient_direction}, {', '.join(hex_colors)})"
            else:
                # Fallback to primary color if gradient extraction fails
                bg_color = brand_guidelines.get("primary_color", "#2674ED")
                using_gradient = False
        elif button_color_type == "Secondary Color":
            bg_color = brand_guidelines.get("secondary_color", "#4A90E2")
        elif button_color_type == "Accent Color":
            bg_color = brand_guidelines.get("accent_color", "#FF5722")
        else:  # Primary Color or fallback
            bg_color = brand_guidelines.get("primary_color", "#2674ED")

        # Always use white text for buttons for better visibility
        # text_color remains "#FFFFFF"

        # Apply button style (rounded vs cornered)
        if brand_guidelines.get("button_style") == "Rounded":
            border_radius = brand_guidelines.get("border_radius") or border_radius
        else:
            border_radius = "0px"  # Cornered style

        # Apply button size
        cta_size = brand_guidelines.get("cta_size", "Medium")
        if cta_size == "Small":
            padding = "8px 16px"
            font_size = "14px"
        elif cta_size == "Large":
            padding = "16px 32px"
            font_size = "18px"

        # Apply font settings
        font_family = f"{brand_guidelines.get('font', 'Arial')}, sans-serif"
        font_size = brand_guidelines.get("font_size") or font_size
        font_weight = brand_guidelines.get("font_weight", "Bold").lower()

    # Create the button HTML
    if using_gradient and gradient_css:
        # Button with gradient background
        return f'''
        <div style="text-align: center; margin: 25px 0; clear: both;">
            <a href="{url}" class="button" style="display: inline-block; background: {gradient_css} !important; color: {text_color} !important;
                                padding: {padding}; text-decoration: none; border-radius: {border_radius};
                                font-weight: {font_weight}; font-size: {font_size}; font-family: {font_family}; border: none;
                                min-width: 150px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
                {cta_text}
            </a>
        </div>
        '''
    else:
        # Button with solid background color
        return f'''
        <div style="text-align: center; margin: 25px 0; clear: both;">
            <a href="{url}" class="button" style="display: inline-block; background-color: {bg_color} !important; color: {text_color} !important;
                                padding: {padding}; text-decoration: none; border-radius: {border_radius};
                                font-weight: {font_weight}; font-size: {font_size}; font-family: {font_family};
                                min-width: 150px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
                {cta_text}
            </a>
        </div>
        '''

def process_product_name(text: str, product_name: str, product_url: str, communication_settings: Dict[str, Any] = None, brand_guidelines: Dict[str, Any] = None) -> str:
    """
    Process the first occurrence of product name in text and make it a link.

    Args:
        text: Text content that may contain product name
        product_name: Name of the product
        product_url: URL of the product
        communication_settings: Dictionary containing UTM parameters
        brand_guidelines: Dictionary containing brand guidelines

    Returns:
        Text with linked product name
    """
    global product_replaced

    if product_replaced == 0:
        if not product_name or not product_url:
            return text

        # Default link color
        link_color = "#0366d6"

        # Apply brand guidelines if available
        if brand_guidelines:
            # Use primary color for links
            link_color = brand_guidelines.get("primary_color") or link_color

        # Escape special regex characters in product name
        escaped_product_name = re.escape(product_name)

        # Find first occurrence of product name (case insensitive)
        pattern = re.compile(f'({escaped_product_name})', re.IGNORECASE)
        match = pattern.search(text)

        if match:
            # Add UTM parameters to product URL
            if communication_settings:
                product_url = add_utm_parameters(product_url, communication_settings)
            product_replaced = 1
            # Replace first occurrence with linked version
            return text[:match.start()] + f'<a href="{product_url}" style="color: {link_color}; text-decoration: none;">{match.group(1)}</a>' + text[match.end():]

        return text
    else:
        return text

def create_html_email_template(body_content: str, company_name: str = "OpenEngage", product_url: str = None, recipient_email: str = None, brand_guidelines: Dict[str, Any] = None, template_name: str = None) -> str:
    """
    Create a complete HTML email template using brand guidelines.

    This function creates a complete HTML email with proper styling, CTA buttons,
    and footer. It automatically loads custom CTA text from template_ctas.json
    if a template_name is provided, otherwise uses default CTA text.

    Args:
        body_content: HTML formatted email body content
        company_name: Name of the company sending the email
        product_url: URL of the product to link the CTA button
        recipient_email: Email address of the recipient for unsubscribe link generation
        brand_guidelines: Dictionary containing brand guidelines for styling
        template_name: Name of the template for CTA lookup in template_ctas.json

    Returns:
        Complete HTML email with embedded CTA button and styling
    """
    # Process CTA placement
    if product_url:
        # Always add a CTA button
        try:
            # Get CTA text using the centralized helper function
            cta_text = get_cta_text_for_template(template_name, 'Learn More')

            # Create the CTA button with the determined text
            print(f"DEBUG: Creating CTA button with text: '{cta_text}' for template: '{template_name}'")
            cta_button = create_cta_button(cta_text, product_url, brand_guidelines)

            # Try to insert after the third paragraph
            paragraphs = list(re.split(r'</p>\s*<p[^>]*>', body_content))
            print(f"DEBUG: Split body content into {len(paragraphs)} paragraphs for CTA insertion")

            if len(paragraphs) >= 4:
                # Insert after the third paragraph (index 2)
                insert_position = 3  # After the third paragraph (0-indexed)
                paragraphs.insert(insert_position, cta_button)
                body_content = '</p><p>'.join(paragraphs)
                print(f"DEBUG: Added CTA button after third paragraph (position {insert_position})")
            elif len(paragraphs) == 3:
                # If only three paragraphs, insert after the second one
                paragraphs.insert(2, cta_button)
                body_content = '</p><p>'.join(paragraphs)
                print("DEBUG: Added CTA button after second paragraph (3 paragraphs total)")
            elif len(paragraphs) == 2:
                # If only two paragraphs, insert after both
                paragraphs.insert(2, cta_button)
                body_content = '</p><p>'.join(paragraphs)
                print("DEBUG: Added CTA button after second paragraph (2 paragraphs total)")
            else:
                # If only one paragraph, try to insert it after a reasonable point
                content = paragraphs[0]

                # Try to find a good break point (end of a sentence in the middle)
                sentences = re.split(r'([.!?])\s+', content)
                if len(sentences) > 2:
                    # Find a midpoint to insert the button
                    midpoint = len(sentences) // 2
                    if midpoint % 2 == 1:  # Ensure we're after a punctuation
                        midpoint += 1

                    # Reconstruct with button in the middle
                    first_half = ''.join(sentences[:midpoint])
                    second_half = ''.join(sentences[midpoint:])
                    body_content = first_half + cta_button + second_half
                    logger.debug("Added CTA button in the middle of a single paragraph")
                else:
                    # No good break point, just append the button
                    body_content = content + cta_button
                    logger.debug("Appended CTA button to single paragraph content")
        except Exception as e:
            # If there's an error with the paragraph approach, try a simpler approach
            try:
                # Get CTA text using the centralized helper function (fallback)
                cta_text = get_cta_text_for_template(template_name, 'Learn More')
                print(f"DEBUG: Using CTA '{cta_text}' (fallback approach)")

                cta_button = create_cta_button(cta_text, product_url, brand_guidelines)

                # Try a simpler paragraph split
                parts = body_content.split('</p>')
                if len(parts) >= 4:
                    # Insert after the third paragraph
                    parts.insert(3, cta_button)
                    body_content = '</p>'.join(parts)
                    logger.debug("Added CTA button after third paragraph (simple approach)")
                elif len(parts) >= 3:
                    # Insert after the second paragraph
                    parts.insert(2, cta_button)
                    body_content = '</p>'.join(parts)
                    logger.debug("Added CTA button after second paragraph (simple approach)")
                else:
                    # Just append the button
                    body_content = body_content + cta_button
                    logger.debug("Appended CTA button to content (fallback)")
            except Exception as inner_e:
                logger.warning(f"Error adding CTA button: {str(e)}, then {str(inner_e)}")

    # Default values
    font_family = "Arial, sans-serif"
    font_size = "16px"
    text_color = "#333333"
    link_color = "#2674ED"
    bg_color = "#f5f5f5"
    container_bg = "#FFFFFF"  # Always white for email container
    button_bg_color = "#2674ED"
    button_gradient = ""
    using_gradient = False

    # Apply brand guidelines if available
    if brand_guidelines:
        # Typography settings
        font_family = f"{brand_guidelines.get('font', 'Arial')}, sans-serif"
        font_size = brand_guidelines.get('font_size', '16px')

        # Color settings
        text_color = brand_guidelines.get('text_color') or text_color
        link_color = brand_guidelines.get('primary_color') or link_color
        bg_color = brand_guidelines.get('background_color') or bg_color
        # Always use white for the email container background for better readability
        container_bg = brand_guidelines.get('background_color') or bg_color

        # Get button color based on the selected type
        button_color_type = brand_guidelines.get("button_color_type", "Primary Color")

        # Check if we're using a gradient
        if button_color_type == "Custom Color":
            button_bg_color = brand_guidelines.get("button_custom_color", "#000000")
        elif button_color_type == "Gradient" and brand_guidelines.get("has_gradient", False):
            using_gradient = True
            gradient_colors = brand_guidelines.get("gradient_colors", "")
            gradient_direction = brand_guidelines.get("gradient_direction", "to right")

            # Extract colors for visualization
            import re
            hex_colors = re.findall(r'#[0-9a-fA-F]{3,8}', str(gradient_colors))
            if hex_colors and len(hex_colors) >= 2:
                button_gradient = f"linear-gradient({gradient_direction}, {', '.join(hex_colors)})"
            else:
                # Fallback to primary color if gradient extraction fails
                button_bg_color = brand_guidelines.get("primary_color", "#2674ED")
                using_gradient = False
        elif button_color_type == "Secondary Color":
            button_bg_color = brand_guidelines.get("secondary_color", "#4A90E2")
        elif button_color_type == "Accent Color":
            button_bg_color = brand_guidelines.get("accent_color", "#FF5722")
        else:  # Primary Color or fallback
            button_bg_color = brand_guidelines.get("primary_color", "#2674ED")

    return f'''
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email</title>
        <style>
            body {{
                font-family: {font_family};
                line-height: 1.6;
                color: {text_color};
                margin: 0;
                padding: 0;
                background-color: {bg_color};
            }}
            .email-container {{
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: {container_bg};
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }}
            .email-body {{
                padding: 10px 0;
                font-size: {font_size};
            }}
            .email-footer {{
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                font-size: 12px;
                color: #777;
                text-align: center;
            }}
            a {{
                color: {link_color};
                text-decoration: none;
            }}
            a:hover {{
                text-decoration: underline;
            }}
            .button {{
                display: inline-block;
                color: #FFFFFF !important; /* Always use white text for buttons */
                padding: 12px 24px;
                text-decoration: none;
                border-radius: {brand_guidelines.get('border_radius', '4px') if brand_guidelines and brand_guidelines.get('button_style') == 'Rounded' else '0px'};
                font-weight: bold;
                text-align: center;
                {f"background: {button_gradient} !important;" if using_gradient and button_gradient else f"background-color: {button_bg_color} !important;"}
                {'' if using_gradient else 'border: 1px solid ' + button_bg_color + ';'}
            }}
            .button:hover {{
                opacity: 0.9;
            }}
            p {{
                font-family: {font_family};
                font-size: {font_size};
                color: {text_color};
            }}
        </style>
    </head>
    <body>
        <div class="email-container">
            <div class="email-body">
                {body_content}
            </div>

            <div class="email-footer">
                <p>This email was sent by {company_name}. To unsubscribe, click <a href="{create_unsubscribe_link(recipient_email) if recipient_email else '#'}">here</a>.</p>
            </div>
        </div>
    </body>
    </html>
    '''
